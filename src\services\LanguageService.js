import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEY = 'appLanguage';

const resources = {
  en: {
    translation: {
      Welcome: "Welcome to AgroAI",
      WelcomeBackUser: "Welcome back, {{name}}!",
      ChangeLanguage: "Change Language",
      QuickActions: "Quick Actions",
      FarmStats: "Farm Stats",
      FarmSize: "Farm Size (acres)",
      Acres: "Acres",
      WeatherForecast: "Weather Forecast",
      CropRecommendation: "Crop Recommendation",
      DiseaseDetection: "Disease Detection",
      Location: "Location",
      TakePhoto: "Take Photo",
      SelectLanguage: "Select Your Language",
      Continue: "Continue",
      Login: "Login",
      Register: "Register",
      Healthy: "Healthy",
      Treatment: "Treatment",
      Error: "Error",
      Success: "Success",
      FillAllFields: "Please fill all the fields.",
      InvalidMobile: "Please enter a valid 10-digit mobile number.",
      AccountCreated: "Account created successfully! You are now logged in.",
      AccountCreateFailed: "Failed to create account. Please try again.",
      NoAccountFound: "No account found with this mobile number. Please register.",
      AlreadyHaveAccount: "Already have an account?",
      DontHaveAccount: "Don't have an account?",
      GoBack: "Go Back",
      PermissionDenied: "Permission Denied",
      LocationPermission: "Location access is needed to provide localized weather and advice.",
      MicPermission: "Microphone access is needed for the voice assistant feature.",
      CameraPermission: "Camera access is needed for the disease detection feature.",
      RecommendationError: "Failed to get recommendations",
      CaptureError: "Failed to capture image",
      SelectImageError: "Failed to select image",
      AnalyzeError: "Failed to analyze image",
      WeatherError: "Failed to fetch weather data",
      VoiceDemoError: "Voice recognition not available in this demo",
      InitError: "Failed to initialize the app. Please restart.",
      EnterName: "Enter your name",
      EnterMobileNumber: "Enter your mobile number",
      EnterLandArea: "Enter land area",
      CameraPermissionError: 'Failed to request camera permission.',
      Analyzing: 'Analyze Image',
      Home: 'Home',
      Crops: 'Crops',
      Disease: 'Disease',
      Weather: 'Weather',
      Gallery: 'Choose from Gallery',
      Ok: 'Ok',
      Cancel: 'Cancel',
      Submit: 'Submit',
      Back: 'Back',
      Next: 'Next',
      Done: 'Done',
      Save: 'Save',
      Edit: 'Edit',
      Delete: 'Delete',
      Add: 'Add',
      View: 'View',
      Search: 'Search',
      Clear: 'Clear',
      Close: 'Close',
      Loading: 'Loading',
      Yes: 'Yes',
      No: 'No',
      Warning: 'Warning',
      Info: 'Info',
      Status: 'Status',
      Retry: 'Retry',
      Settings: 'Settings',
      Profile: 'Profile',
      Logout: 'Logout',
      About: 'About',
      Help: 'Help',
      Terms: 'Terms',
      Privacy: 'Privacy',
      Notifications: 'Notifications',
      Language: 'Language',
      Theme: 'Theme',
      Send: 'Send',
      Message: 'Message',
      Call: 'Call',
      Email: 'Email',
      Password: 'Password',
      Username: 'Username',
      Name: 'Name',
      Mobile: 'Mobile',
      Number: 'Number',
      Code: 'Code',
      Verification: 'Verification',
      Resend: 'Resend',
      Update: 'Update',
      Skip: 'Skip',
      Photo: 'Photo',
      Camera: 'Camera',
      Image: 'Image',
      Video: 'Video',
      Audio: 'Audio',
      Record: 'Record',
      Play: 'Play',
      Pause: 'Pause',
      Stop: 'Stop',
      Voice: 'Voice',
      Assistant: 'Assistant',
      Microphone: 'Microphone',
      Speaker: 'Speaker',
      Volume: 'Volume',
      Mute: 'Mute',
      Unmute: 'Unmute',
      Chat: 'Chat',
      Users: 'Users',
      Groups: 'Groups',
      Contacts: 'Contacts',
      Friends: 'Friends',
      Requests: 'Requests',
      Pending: 'Pending',
      Accepted: 'Accepted',
      Rejected: 'Rejected',
      Blocked: 'Blocked',
      Unblock: 'Unblock',
      Report: 'Report',
      Spam: 'Spam',
      Scam: 'Scam',
      Phishing: 'Phishing',
      Hacking: 'Hacking',
      Abuse: 'Abuse',
      Harassment: 'Harassment',
      Violence: 'Violence',
      Hate: 'Hate',
      Speech: 'Speech',
      Nudity: 'Nudity',
      Pornography: 'Pornography',
      Self: 'Self',
      Harm: 'Harm',
      Suicide: 'Suicide',
      Impersonation: 'Impersonation',
      Copyright: 'Copyright',
      Trademark: 'Trademark',
      Infringement: 'Infringement',
      Other: 'Other',
    }
  },
  hi: {
    translation: {
      welcome: "एग्रोएआई में आपका स्वागत है",
      welcomeBackUser: "वापसी पर स्वागत है, {{name}}!",
      changeLanguage: "भाषा बदलें",
      quickActions: "त्वरित क्रियाएँ",
      farmStats: "खेत आँकड़े",
      farmSize: "खेत का आकार (एकड़)",
      acres: "एकड़",
      weatherForecast: "मौसम पूर्वानुमान",
      cropRecommendation: "फसल सिफारिश",
      diseaseDetection: "रोग पहचान",
      location: "स्थान",
      selectLanguage: "अपनी भाषा चुनें",
      continue: "जारी रखें",
      login: "लॉग इन करें",
      register: "पंजीकरण करें",
      healthy: "स्वस्थ",
      treatment: "उपचार",
      error: "त्रुटि",
      success: "सफलता",
      fillAllFields: "कृपया सभी फ़ील्ड भरें।",
      invalidMobile: "कृपया एक मान्य 10-अंकीय मोबाइल नंबर दर्ज करें।",
      accountCreated: "खाता सफलतापूर्वक बनाया गया! अब आप लॉग इन हैं।",
      accountCreateFailed: "खाता बनाने में विफल। कृपया पुनः प्रयास करें।",
      noAccountFound: "इस मोबाइल नंबर से कोई खाता नहीं मिला। कृपया पंजीकरण करें।",
      alreadyHaveAccount: "पहले से ही एक खाता है?",
      dontHaveAccount: "खाता नहीं है?",
      goBack: "वापस जाओ",
      permissionDenied: "अनुमति अस्वीकृत",
      locationPermission: "स्थानीय मौसम और सलाह प्रदान करने के लिए स्थान की पहुंच आवश्यक है।",
      micPermission: "वॉयस असिस्टेंट सुविधा के लिए माइक्रोफोन की पहुंच आवश्यक है।",
      cameraPermission: "रोग पहचान सुविधा के लिए कैमरे तक पहुंच आवश्यक है।",
      recommendationError: "सिफारिशें प्राप्त करने में विफल",
      captureError: "छवि कैप्चर करने में विफल",
      selectImageError: "छवि का चयन करने में विफल",
      analyzeError: "छवि का विश्लेषण करने में विफल",
      weatherError: "मौसम डेटा लाने में विफल",
      voiceDemoError: "इस डेमो में आवाज पहचान उपलब्ध नहीं है",
      initError: "ऐप को प्रारंभ करने में विफल। कृपया पुनरारंभ करें।",
      enterName: "अपना नाम दर्ज करें",
      enterMobileNumber: "अपना मोबाइल नंबर दर्ज करें",
      enterLandArea: "जमीन का क्षेत्रफल दर्ज करें",
      cameraPermissionError: 'कैमरा अनुमति का अनुरोध करने में विफल।',
      selectImageError: 'छवि चुनने में त्रुटि।',
      analyzing: 'छवि का विश्लेषण करें',
      analyzeError: 'छवि का विश्लेषण करने में त्रुटि।',
      home: 'होम',
      crops: 'फसलें',
      disease: 'रोग',
      weather: 'मौसम',
      gallery: 'गैलरी से चुनें',
      ok: 'ठीक है',
      cancel: 'रद्द करें',
      submit: 'प्रस्तुत करें',
      back: 'वापस',
      next: 'अगला',
      done: 'पूर्ण',
      save: 'सहेजें',
      edit: 'संपादित करें',
      delete: 'हटाएं',
      add: 'जोड़ें',
      view: 'देखें',
      search: 'खोजें',
      clear: 'साफ़ करें',
      close: 'बंद करें',
      loading: 'लोड हो रहा है',
      yes: 'हाँ',
      no: 'नहीं',
      warning: 'चेतावनी',
      info: 'जानकारी',
      status: 'स्थिति',
      retry: 'पुनः प्रयास करें',
      settings: 'सेटिंग्स',
      profile: 'प्रोफ़ाइल',
      logout: 'लॉग आउट',
      about: 'बारे में',
      help: 'सहायता',
      terms: 'शर्तें',
      privacy: 'गोपनीयता',
      notifications: 'सूचनाएं',
      language: 'भाषा',
      theme: 'थीम',
      send: 'भेजें',
      message: 'संदेश',
      call: 'कॉल करें',
      email: 'ईमेल',
      password: 'पासवर्ड',
      username: 'उपयोगकर्ता नाम',
      name: 'नाम',
      mobile: 'मोबाइल',
      number: 'संख्या',
      code: 'कोड',
      verification: 'सत्यापन',
      resend: 'पुनः भेजें',
      update: 'अपडेट करें',
      skip: 'छोड़ें',
      photo: 'फोटो',
      camera: 'कैमरा',
      image: 'छवि',
      video: 'वीडियो',
      audio: 'ऑडियो',
      record: 'रिकॉर्ड करें',
      play: 'चलाएं',
      pause: 'रोकें',
      stop: 'रोकें',
      voice: 'आवाज',
      assistant: 'सहायक',
      microphone: 'माइक्रोफोन',
      speaker: 'स्पीकर',
      volume: 'आवाज',
      mute: 'म्यूट करें',
      unmute: 'अनम्यूट करें',
      chat: 'चैट',
      users: 'उपयोगकर्ता',
      groups: 'समूह',
      contacts: 'संपर्क',
      friends: 'मित्र',
      requests: 'अनुरोध',
      pending: 'लंबित',
      accepted: 'स्वीकृत',
      rejected: 'अस्वीकृत',
      blocked: 'अवरुद्ध',
      unblock: 'अनब्लॉक करें',
      report: 'रिपोर्ट करें',
      spam: 'स्पैम',
      scam: 'घोटाला',
      phishing: 'फ़िशिंग',
      hacking: 'हैक',
      abuse: 'दुरुपयोग',
      harassment: 'उत्पीड़न',
      violence: 'हिंसा',
      hate: 'घृणा',
      speech: 'भाषण',
      nudity: 'नग्नता',
      pornography: 'अश्लीलता',
      self: 'स्वयं',
      harm: 'नुकसान',
      suicide: 'आत्महत्या',
      impersonation: 'प्रतिरूपण',
      copyright: 'कॉपीराइट',
      trademark: 'ट्रेडमार्क',
      infringement: 'उल्लंघन',
      other: 'अन्य',
    }
  },
  te: {
    translation: {
      welcome: "ఆగ్రోఏఐకి స్వాగతం",
      cropRecommendation: "పంట సిఫార్సు",
      diseaseDetection: "వ్యాధి నిర్ధారణ",
      weather: "వాతావరణం",
      selectLanguage: "మీ భాషను ఎంచుకోండి",
      continue: "కొనసాగించు",
      login: "లాగిన్",
      register: "నమోదు చేసుకోండి",
      welcomeBack: "తిరిగి స్వాగతం!",
      loginToContinue: "కొనసాగించడానికి మీ ఖాతాకు లాగిన్ చేయండి",
      mobileNumber: "మొబైల్ నంబర్",
      createAccount: "కొత్త ఖాతాను సృష్టించండి",
      farmDetails: "ప్రారంభించడానికి మీ వివరాలను నమోదు చేయండి",
      name: "పేరు",
      acresOfLand: "భూమి ఎకరాలు",
      areaOfLand: "భూమి యొక్క ప్రాంతం (ఉదా., గ్రామం, నగరం)",
      gallery: "గ్యాలరీ",
      analyzing: "విశ్లేషిస్తోంది...",
      healthy: "ఆరోగ్యకరమైనది",
      treatment: "చికిత్స",
      error: "లోపం",
      success: "విజయం",
      fillAllFields: "దయచేసి అన్ని ఫీల్డ్‌లను పూరించండి.",
      invalidMobile: "దయచేసి సరైన 10-అంకెల మొబైల్ నంబర్‌ను నమోదు చేయండి.",
      accountCreated: "ఖాతా విజయవంతంగా సృష్టించబడింది! మీరు ఇప్పుడు లాగిన్ అయ్యారు.",
      accountCreateFailed: "ఖాతాను సృష్టించడంలో విఫలమైంది. దయచేసి మళ్లీ ప్రయత్నించండి.",
      welcomeBackUser: "తిరిగి స్వాగతం, {{name}}!",
      noAccountFound: "ఈ మొబైల్ నంబర్‌తో ఖాతా కనుగొనబడలేదు. దయచేసి నమోదు చేసుకోండి.",
      alreadyHaveAccount: "ఇప్పటికే ఖాతా ఉందా?",
      dontHaveAccount: "ఖాతా లేదా?",
      goBack: "వెనుకకు వెళ్ళు",
      permissionDenied: "అనుమతి నిరాకరించబడింది",
      locationPermission: "స్థానికీకరించిన వాతావరణం మరియు సలహాలను అందించడానికి లొకేషన్ యాక్సెస్ అవసరం.",
      micPermission: "వాయిస్ అసిస్టెంట్ ఫీచర్ కోసం మైక్రోఫోన్ యాక్సెస్ అవసరం.",
      cameraPermission: "వ్యాధి నిర్ధారణ ఫీచర్ కోసం కెమెరా యాక్సెస్ అవసరం.",
      recommendationError: "సిఫార్సులను పొందడంలో విఫలమైంది",
      captureError: "చిత్రాన్ని క్యాప్చర్ చేయడంలో విఫలమైంది",
      selectImageError: "చిత్రాన్ని ఎంచుకోవడంలో విఫలమైంది",
      analyzeError: "చిత్రాన్ని విశ్లేషించడంలో విఫలమైంది",
      weatherError: "వాతావరణ డేటాను పొందడంలో విఫలమైంది",
      voiceDemoError: "ఈ డెమోలో వాయిస్ రికగ్నిషన్ అందుబాటులో లేదు",
      initError: "యాప్‌ను ప్రారంభించడంలో విఫలమైంది. దయచేసి పునఃప్రారంభించండి.",
      enterName: "మీ పేరును నమోదు చేయండి",
      enterMobileNumber: "మీ మొబైల్ నంబర్‌ను నమోదు చేయండి",
      enterLandArea: "భూమి విస్తీర్ణాన్ని నమోదు చేయండి",
      farmSize: 'పొలం పరిమాణం (ఎకరాలు)',
      location: 'స్థానం',
      cameraPermissionError: 'కెమెరా అనుమతిని అభ్యర్థించడంలో విఫలమైంది.',
      selectImageError: 'చిత్రాన్ని ఎంచుకోవడంలో లోపం.',
      analyzing: 'చిత్రాన్ని విశ్లేషించండి',
      analyzeError: 'చిత్రాన్ని విశ్లేషించడంలో లోపం.',
      home: 'హోమ్',
      crops: 'పంటలు',
      disease: 'వ్యాధి',
      weather: 'వాతావరణం',
      gallery: 'గ్యాలరీ నుండి ఎంచుకోండి',
      ok: 'సరే',
      cancel: 'రద్దు చేయండి',
      submit: 'సమర్పించు',
      back: 'వెనుకకు',
      next: 'తదుపరి',
      done: 'పూర్తయింది',
      save: 'సేవ్ చేయండి',
      edit: 'సవరించండి',
      delete: 'తొలగించండి',
      add: 'జోడించండి',
      view: 'చూడండి',
      search: 'శోధించండి',
      clear: 'క్లియర్ చేయండి',
      close: 'మూసివేయండి',
      loading: 'లోడ్ అవుతోంది',
      yes: 'అవును',
      no: 'కాదు',
      warning: 'హెచ్చరిక',
      info: 'సమాచారం',
      status: 'స్థితి',
      retry: 'మళ్లీ ప్రయత్నించండి',
      settings: 'సెట్టింగ్‌లు',
      profile: 'ప్రొఫైల్',
      logout: 'లాగ్ అవుట్',
      about: 'గురించి',
      help: 'సహాయం',
      terms: 'నిబంధనలు',
      privacy: 'గోప్యత',
      notifications: 'నోటిఫికేషన్‌లు',
      language: 'భాష',
      theme: 'థీమ్',
      send: 'పంపండి',
      message: 'సందేశం',
      call: 'కాల్ చేయండి',
      email: 'ఇమెయిల్',
      password: 'పాస్‌వర్డ్',
      username: 'వినియోగదారు పేరు',
      name: 'పేరు',
      mobile: 'మొబైల్',
      number: 'సంఖ్య',
      code: 'కోడ్',
      verification: 'ధృవీకరణ',
      resend: 'మళ్లీ పంపండి',
      update: 'నవీకరించండి',
      skip: 'దాటవేయి',
      photo: 'ఫోటో',
      camera: 'కెమెరా',
      image: 'చిత్రం',
      video: 'వీడియో',
      audio: 'ఆడియో',
      record: 'రికార్డ్ చేయండి',
      play: 'ప్లే చేయండి',
      pause: 'పాజ్ చేయండి',
      stop: 'ఆపండి',
      voice: 'వాయిస్',
      assistant: 'సహాయకుడు',
      microphone: 'మైక్రోఫోన్',
      speaker: 'స్పీకర్',
      volume: 'వాల్యూమ్',
      mute: 'మ్యూట్ చేయండి',
      unmute: 'అన్‌మ్యూట్ చేయండి',
      chat: 'చాట్',
      users: 'వినియోగదారులు',
      groups: 'సమూహాలు',
      contacts: 'పరిచయాలు',
      friends: 'స్నేహితులు',
      requests: 'అభ్యర్థనలు',
      pending: 'పెండింగ్‌లో ఉంది',
      accepted: 'ఆమోదించబడింది',
      rejected: 'తిరస్కరించబడింది',
      blocked: 'నిరోధించబడింది',
      unblock: 'అన్‌బ్లాక్ చేయండి',
      report: 'నివేదించండి',
      spam: 'స్పామ్',
      scam: 'స్కామ్',
      phishing: 'ఫిషింగ్',
      hacking: 'హ్యాకింగ్',
      abuse: 'దుర్వినియోగం',
      harassment: 'వేధింపు',
      violence: 'హింస',
      hate: 'ద్వేషం',
      speech: 'ప్రసంగం',
      nudity: 'నగ్నత్వం',
      pornography: 'అశ్లీలత',
      self: 'స్వయం',
      harm: 'హాని',
      suicide: 'ఆత్మహత్య',
      impersonation: 'నకిలీ',
      copyright: 'కాపీరైట్',
      trademark: 'ట్రేడ్‌మార్క్',
      infringement: 'ఉల్లంఘన',
      other: 'ఇతర',
    }
  },
  ta: {
    translation: {
      welcome: "அக்ரோஏஐ-க்கு வரவேற்கிறோம்",
      cropRecommendation: "பயிர் பரிந்துரை",
      diseaseDetection: "நோய் கண்டறிதல்",
      weather: "வானிலை",
      selectLanguage: "உங்கள் மொழியைத் தேர்ந்தெடுக்கவும்",
      continue: "தொடரவும்",
      login: "உள்நுழைய",
      register: "பதிவு செய்ய",
      welcomeBack: "மீண்டும் வரவேற்கிறோம்!",
      loginToContinue: "தொடர உங்கள் கணக்கில் உள்நுழையவும்",
      mobileNumber: "மொபைல் எண்",
      createAccount: "புதிய கணக்கை உருவாக்கவும்",
      farmDetails: "தொடங்குவதற்கு உங்கள் விவரங்களை உள்ளிடவும்",
      name: "பெயர்",
      acresOfLand: "நிலத்தின் ஏக்கர்",
      areaOfLand: "நிலத்தின் பகுதி (எ.கா., கிராமம், நகரம்)",
      gallery: "கேலரி",
      analyzing: "பகுப்பாய்வு செய்கிறது...",
      healthy: "ஆரோக்கியமான",
      treatment: "சிகிச்சை",
      error: "பிழை",
      success: "வெற்றி",
      fillAllFields: "அனைத்து புலங்களையும் நிரப்பவும்.",
      invalidMobile: "சரியான 10 இலக்க மொபைல் எண்ணை உள்ளிடவும்.",
      accountCreated: "கணக்கு வெற்றிகரமாக உருவாக்கப்பட்டது! நீங்கள் இப்போது உள்நுழைந்துள்ளீர்கள்.",
      accountCreateFailed: "கணக்கை உருவாக்க முடியவில்லை. மீண்டும் முயற்சிக்கவும்.",
      welcomeBackUser: "மீண்டும் வருக, {{name}}!",
      noAccountFound: "இந்த மொபைல் எண்ணுடன் கணக்கு எதுவும் இல்லை. பதிவு செய்யவும்.",
      alreadyHaveAccount: "ஏற்கனவே கணக்கு உள்ளதா?",
      dontHaveAccount: "கணக்கு இல்லையா?",
      goBack: "பின் செல்",
      permissionDenied: "அனுமதி மறுக்கப்பட்டது",
      locationPermission: "உள்ளூர் வானிலை மற்றும் ஆலோசனைகளை வழங்க இருப்பிட அணுகல் தேவை.",
      micPermission: "குரல் உதவியாளர் அம்சத்திற்கு மைக்ரோஃபோன் அணுகல் தேவை.",
      cameraPermission: "நோய் கண்டறிதல் அம்சத்திற்கு கேமரா அணுகல் தேவை.",
      recommendationError: "பரிந்துரைகளைப் பெற முடியவில்லை",
      captureError: "படத்தைப் பிடிக்க முடியவில்லை",
      selectImageError: "படத்தைத் தேர்ந்தெடுக்க முடியவில்லை",
      analyzeError: "படத்தை பகுப்பாய்வு செய்ய முடியவில்லை",
      weatherError: "வானிலை தரவைப் பெற முடியவில்லை",
      voiceDemoError: "இந்த டெமோவில் குரல் அடையாளம் கிடைக்கவில்லை",
      initError: "பயன்பாட்டைத் தொடங்குவதில் தோல்வி. தயவுசெய்து மீண்டும் தொடங்கவும்.",
      enterName: "உங்கள் பெயரை உள்ளிடவும்",
      enterMobileNumber: "உங்கள் மொபைல் எண்ணை உள்ளிடவும்",
      enterLandArea: "நிலப் பரப்பளவை உள்ளிடவும்",
      farmSize: 'பண்ணை அளவு (ஏக்கர்கள்)',
      location: 'இடம்',
      cameraPermissionError: 'கேமரா அனுமதியைக் கோர முடியவில்லை.',
      selectImageError: 'படத்தைத் தேர்ந்தெடுப்பதில் பிழை.',
      analyzing: 'படத்தை பகுப்பாய்வு செய்',
      analyzeError: 'படத்தை பகுப்பாய்வு செய்வதில் பிழை.',
      home: 'வீடு',
      crops: 'பயிர்கள்',
      disease: 'நோய்',
      weather: 'வானிலை',
      gallery: 'தொகுப்பு இருந்து தேர்வு செய்',
      ok: 'சரி',
      cancel: 'ரத்துசெய்',
      submit: 'சமர்ப்பி',
      back: 'பின்',
      next: 'அடுத்து',
      done: 'முடிந்தது',
      save: 'சேமி',
      edit: 'திருத்து',
      delete: 'நீக்கு',
      add: 'சேர்',
      view: 'பார்',
      search: 'தேடு',
      clear: 'நீக்கு',
      close: 'மூடு',
      loading: 'ஏற்றுகிறது',
      yes: 'ஆம்',
      no: 'இல்லை',
      warning: 'எச்சரிக்கை',
      info: 'தகவல்',
      status: 'நிலை',
      retry: 'மீண்டும் முயலவும்',
      settings: 'அமைப்புகள்',
      profile: 'சுயவிவரம்',
      logout: 'வெளியேறு',
      about: 'பற்றி',
      help: 'உதவி',
      terms: 'விதிமுறைகள்',
      privacy: 'தனியுரிமை',
      notifications: 'அறிவிப்புகள்',
      language: 'மொழி',
      theme: 'தீம்',
      send: 'அனுப்பு',
      message: 'செய்தி',
      call: 'அழை',
      email: 'மின்னஞ்சல்',
      password: 'கடவுச்சொல்',
      username: 'பயனர்பெயர்',
      name: 'பெயர்',
      mobile: 'மொபைல்',
      number: 'எண்',
      code: 'குறியீடு',
      verification: 'சரிபார்ப்பு',
      resend: 'மீண்டும் அனுப்பு',
      update: 'புதுப்பி',
      skip: 'தவிர்',
      photo: 'புகைப்படம்',
      camera: 'கேமரா',
      image: 'படம்',
      video: 'வீடியோ',
      audio: 'ஆடியோ',
      record: 'பதிவு',
      play: 'இயக்கு',
      pause: 'இடைநிறுத்து',
      stop: 'நிறுத்து',
      voice: 'குரல்',
      assistant: 'உதவியாளர்',
      microphone: 'மைக்ரோஃபோன்',
      speaker: 'சபாநாயகர்',
      volume: 'தொகுதி',
      mute: 'முடக்கு',
      unmute: 'ஒலி எழுப்பு',
      chat: 'அரட்டை',
      users: 'பயனர்கள்',
      groups: 'குழுக்கள்',
      contacts: 'தொடர்புகள்',
      friends: 'நண்பர்கள்',
      requests: 'கோரிக்கைகள்',
      pending: 'நிலுவையில்',
      accepted: 'ஏற்றுக்கொள்ளப்பட்டது',
      rejected: 'நிராகரிக்கப்பட்டது',
      blocked: 'தடுக்கப்பட்டது',
      unblock: 'தடையை நீக்கு',
      report: 'புகாரளி',
      spam: 'ஸ்பேம்',
      scam: 'மோசடி',
      phishing: 'ఫిషింగ్',
      hacking: 'ஹேக்கிங்',
      abuse: 'துஷ்பிரயோகம்',
      harassment: 'துன்புறுத்தல்',
      violence: 'வன்முறை',
      hate: 'வெறுப்பு',
      speech: 'பேச்சு',
      nudity: 'நிர்வாணம்',
      pornography: 'ஆபாசம்',
      self: 'சுய',
      harm: 'தீங்கு',
      suicide: 'தற்கொலை',
      impersonation: 'ஆள்மாறாட்டம்',
      copyright: 'பதிப்புரிமை',
      trademark: 'வர்த்தகமுத்திரை',
      infringement: 'மீறல்',
      other: 'மற்றவை',
    }
  },
  kn: {
    translation: {
      welcome: "ಅಗ್ರೋಎಐಗೆ ಸ್ವಾಗತ",
      cropRecommendation: "ಬೆಳೆ ಶಿಫಾರಸು",
      diseaseDetection: "ರೋಗ ಪತ್ತೆ",
      weather: "ಹವಾಮಾನ",
      selectLanguage: "ನಿಮ್ಮ ಭಾಷೆಯನ್ನು ಆಯ್ಕೆಮಾಡಿ",
      continue: "ಮುಂದುವರಿಸಿ",
      login: "ಲಾಗಿನ್ ಮಾಡಿ",
      register: "ನೋಂದಣಿ ಮಾಡಿ",
      welcomeBack: "ಮರಳಿ ಸ್ವಾಗತ!",
      loginToContinue: "ಮುಂದುವರಿಸಲು ನಿಮ್ಮ ಖಾತೆಗೆ ಲಾಗಿನ್ ಮಾಡಿ",
      mobileNumber: "ಮೊಬೈಲ್ ನಂಬರ್",
      createAccount: "ಹೊಸ ಖಾತೆಯನ್ನು ರಚಿಸಿ",
      farmDetails: "ಪ್ರಾರಂಭಿಸಲು ನಿಮ್ಮ ವಿವರಗಳನ್ನು ನಮೂದಿಸಿ",
      name: "ಹೆಸರು",
      acresOfLand: "ಭೂಮಿಯ ಎಕರೆ",
      areaOfLand: "ಭೂಮಿಯ ಪ್ರದೇಶ (ಉದಾ., ಗ್ರಾಮ, ನಗರ)",
      gallery: "ಗ್ಯಾಲರಿ",
      analyzing: "ವಿಶ್ಲೇಷಿಸಲಾಗುತ್ತಿದೆ...",
      healthy: "ಆರೋಗ್ಯಕರ",
      treatment: "ಚಿಕಿತ್ಸೆ",
      error: "ದೋಷ",
      success: "ಯಶಸ್ಸು",
      fillAllFields: "ದಯವಿಟ್ಟು ಎಲ್ಲಾ ಕ್ಷೇತ್ರಗಳನ್ನು ಭರ್ತಿ ಮಾಡಿ.",
      invalidMobile: "ದಯವಿಟ್ಟು ಮಾನ್ಯ 10-ಅಂಕಿಯ ಮೊಬೈಲ್ ಸಂಖ್ಯೆಯನ್ನು ನಮೂದಿಸಿ.",
      accountCreated: "ಖಾತೆಯನ್ನು ಯಶಸ್ವಿಯಾಗಿ ರಚಿಸಲಾಗಿದೆ! ನೀವು ಈಗ ಲಾಗ್ ಇನ್ ಆಗಿದ್ದೀರಿ.",
      accountCreateFailed: "ಖಾತೆಯನ್ನು ರಚಿಸಲು ವಿಫಲವಾಗಿದೆ. ದಯವಿಟ್ಟು ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ.",
      welcomeBackUser: "ಮರಳಿ ಸ್ವಾಗತ, {{name}}!",
      noAccountFound: "ಈ ಮೊಬೈಲ್ ಸಂಖ್ಯೆಯೊಂದಿಗೆ ಯಾವುದೇ ಖಾತೆ ಕಂಡುಬಂದಿಲ್ಲ. ದಯವಿಟ್ಟು ನೋಂದಾಯಿಸಿ.",
      alreadyHaveAccount: "ಈಗಾಗಲೇ ಖಾತೆ ಇದೆಯೇ?",
      dontHaveAccount: "ಖಾತೆ ಇಲ್ಲವೇ?",
      goBack: "ಹಿಂದಕ್ಕೆ ಹೋಗಿ",
      permissionDenied: "ಅನುಮತಿ ನಿರಾಕರಿಸಲಾಗಿದೆ",
      locationPermission: "ಸ್ಥಳೀಯ ಹವಾಮಾನ ಮತ್ತು ಸಲಹೆಯನ್ನು ಒದಗಿಸಲು ಸ್ಥಳ ಪ್ರವೇಶದ ಅಗತ್ಯವಿದೆ.",
      micPermission: "ಧ್ವನಿ ಸಹಾಯಕ ವೈಶಿಷ್ಟ್ಯಕ್ಕಾಗಿ ಮೈಕ್ರೊಫೋನ್ ಪ್ರವೇಶದ ಅಗತ್ಯವಿದೆ.",
      cameraPermission: "ರೋಗ ಪತ್ತೆ ವೈಶಿಷ್ಟ್ಯಕ್ಕಾಗಿ ಕ್ಯಾಮೆರಾ ಪ್ರವೇಶದ ಅಗತ್ಯವಿದೆ.",
      recommendationError: "ಶಿಫಾರಸುಗಳನ್ನು ಪಡೆಯಲು ವಿಫಲವಾಗಿದೆ",
      captureError: "ಚಿತ್ರವನ್ನು ಸೆರೆಹಿಡಿಯಲು ವಿಫಲವಾಗಿದೆ",
      selectImageError: "ಚಿತ್ರವನ್ನು ಆಯ್ಕೆ ಮಾಡಲು ವಿಫಲವಾಗಿದೆ",
      analyzeError: "ಚಿತ್ರವನ್ನು ವಿಶ್ಲೇಷಿಸಲು ವಿಫಲವಾಗಿದೆ",
      weatherError: "ಹವಾಮಾನ ಡೇಟಾವನ್ನು ತರಲು ವಿಫಲವಾಗಿದೆ",
      voiceDemoError: "ಈ ಡೆಮೋದಲ್ಲಿ ಧ್ವನಿ ಗುರುತಿಸುವಿಕೆ ಲಭ್ಯವಿಲ್ಲ",
      initError: "ಅಪ್ಲಿಕೇಶನ್ ಅನ್ನು ಪ್ರಾರಂಭಿಸಲು ವಿಫలವಾಗಿದೆ. ದಯವಿಟ್ಟು ಮರುಪ್ರಾರಂಭಿಸಿ.",
      enterName: "ನಿಮ್ಮ ಹೆಸರನ್ನು ನಮೂದಿಸಿ",
      enterMobileNumber: "ನಿಮ್ಮ ಮೊಬೈಲ್ ಸಂಖ್ಯೆಯನ್ನು ನಮೂದಿಸಿ",
      enterLandArea: "ಭೂಪ್ರದೇಶವನ್ನು ನಮೂದಿಸಿ",
      farmSize: 'ಫಾರ್ಮ್ ಗಾತ್ರ (ಎಕರೆ)',
      location: 'ಸ್ಥಳ',
      cameraPermissionError: 'ಕ್ಯಾಮೆರಾ ಅನುಮತಿಯನ್ನು ವಿನಂತಿಸಲು ವಿಫಲವಾಗಿದೆ.',
      selectImageError: 'ಚಿತ್ರವನ್ನು ಆಯ್ಕೆ ಮಾಡಲು ದೋಷ.',
      analyzing: 'ಚಿತ್ರವನ್ನು ವಿಶ್ಲೇಷಿಸಿ',
      analyzeError: 'ಚಿತ್ರವನ್ನು ವಿಶ್ಲೇಷಿಸುವಲ್ಲಿ ದೋಷ.',
      home: 'ಮನೆ',
      crops: 'ಬೆಳೆಗಳು',
      disease: 'ರೋಗ',
      weather: 'ಹವಾಮಾನ',
      gallery: 'ಗ್ಯಾಲರಿಯಿಂದ ಆರಿಸಿ',
      ok: 'ಸರಿ',
      cancel: 'ರದ್ದುಮಾಡಿ',
      submit: 'ಸಲ್ಲಿಸಿ',
      back: 'ಹಿಂದೆ',
      next: 'ಮುಂದೆ',
      done: 'ಮುಗಿದಿದೆ',
      save: 'ಉಳಿಸಿ',
      edit: 'ಸಂಪಾದಿಸಿ',
      delete: 'ಅಳಿಸಿ',
      add: 'ಸೇರಿಸಿ',
      view: 'ವೀಕ್ಷಿಸಿ',
      search: 'ಹುಡುಕಿ',
      clear: 'ಅಳಿಸಿ',
      close: 'ಮುಚ್ಚಿ',
      loading: 'ಲೋಡ್ ಆಗುತ್ತಿದೆ',
      yes: 'ಹೌದು',
      no: 'ಇಲ್ಲ',
      warning: 'ಎಚ್ಚರಿಕೆ',
      info: 'ಮಾಹಿತಿ',
      status: 'ಸ್ಥಿತಿ',
      retry: 'ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ',
      settings: 'ಸಂಯೋಜನೆಗಳು',
      profile: 'ಪ್ರೊಫೈಲ್',
      logout: 'ಲಾಗ್ ಔಟ್',
      about: 'ಬಗ್ಗೆ',
      help: 'ಸಹಾಯ',
      terms: 'ನಿಯಮಗಳು',
      privacy: 'ಗೌಪ್ಯತೆ',
      notifications: 'ಅಧಿಸೂಚನೆಗಳು',
      language: 'ಭಾಷೆ',
      theme: 'ಥೀಮ್',
      send: 'ಕಳುಹಿಸಿ',
      message: 'ಸಂದೇಶ',
      call: 'ಕರೆ ಮಾಡಿ',
      email: 'ಇಮೇಲ್',
      password: 'ಪಾಸ್ವರ್ಡ್',
      username: 'ಬಳಕೆದಾರಹೆಸರು',
      name: 'ಹೆಸರು',
      mobile: 'ಮೊಬೈಲ್',
      number: 'ಸಂಖ್ಯೆ',
      code: 'ಕೋಡ್',
      verification: 'ಪರಿಶೀಲನೆ',
      resend: 'ಮರುಕಳುಹಿಸಿ',
      update: 'ನವೀಕರಿಸಿ',
      skip: 'ಸ್ಕಿಪ್ ಮಾಡಿ',
      photo: 'ಫೋಟೋ',
      camera: 'ಕ್ಯಾಮೆರಾ',
      image: 'ಚಿತ್ರ',
      video: 'ವೀಡಿಯೊ',
      audio: 'ಆಡಿಯೋ',
      record: 'ರೆಕಾರ್ಡ್ ಮಾಡಿ',
      play: 'ಪ್ಲೇ ಮಾಡಿ',
      pause: 'ವಿರಾಮಗೊಳಿಸಿ',
      stop: 'ನಿಲ್ಲಿಸಿ',
      voice: 'ಧ್ವನಿ',
      assistant: 'ಸಹಾಯಕ',
      microphone: 'ಮೈಕ್ರೊಫೋನ್',
      speaker: 'ಸ್ಪೀಕರ್',
      volume: 'ವಾಲ್ಯೂಮ್',
      mute: 'ಮ್ಯೂಟ್ ಮಾಡಿ',
      unmute: 'ಅನ್‌ಮ್ಯೂಟ್ ಮಾಡಿ',
      chat: 'ಚಾಟ್',
      users: 'ಬಳಕೆದಾರರು',
      groups: 'ಗುಂಪುಗಳು',
      contacts: 'ಸಂಪರ್ಕಗಳು',
      friends: 'ಸ್ನೇಹಿತರು',
      requests: 'ವಿನಂತಿಗಳು',
      pending: 'ಬಾಕಿ ಉಳಿದಿದೆ',
      accepted: 'ಸ್ವೀಕರಿಸಲಾಗಿದೆ',
      rejected: 'ತಿರಸ್ಕರಿಸಲಾಗಿದೆ',
      blocked: 'ನಿರ್ಬಂಧಿಸಲಾಗಿದೆ',
      unblock: 'ಅನಿರ್ಬಂಧಿಸಿ',
      report: 'ವರದಿ ಮಾಡಿ',
      spam: 'ಸ್ಪ್ಯಾಮ್',
      scam: 'ಹಗರಣ',
      phishing: 'ಫಿಶಿಂಗ್',
      hacking: 'ಹ್ಯಾಕಿಂಗ್',
      abuse: 'ದುರುಪಯೋಗ',
      harassment: 'ಕಿರುಕುಳ',
      violence: 'ಹಿಂಸೆ',
      hate: 'ದ್ವೇಷ',
      speech: 'ಭಾಷಣ',
      nudity: 'ನಗ್ನತೆ',
      pornography: 'ಅಶ್లీೕಲತೆ',
      self: 'ಸ್ವಯಂ',
      harm: 'ಹಾನಿ',
      suicide: 'ಆತ್ಮಹತ್ಯೆ',
      impersonation: ' प्रतिरूपण',
      copyright: 'ಕೃತಿಸ್ವಾಮ್ಯ',
      trademark: 'ಟ್ರೇಡ್ಮಾರ್ಕ್',
      infringement: 'ಉಲ್ಲಂಘನೆ',
      other: 'ಇತರೆ',
    }
  },
  mr: {
    translation: {
      welcome: "ॲग्रोएआय मध्ये आपले स्वागत आहे",
      cropRecommendation: "पीक शिफारस",
      diseaseDetection: "रोग ओळख",
      weather: "हवामान",
      selectLanguage: "तुमची भाषा निवडा",
      continue: "पुढे जा",
      login: "लॉग इन करा",
      register: "नोंदणी करा",
      welcomeBack: "पुन्हा स्वागत आहे!",
      loginToContinue: "पुढे जाण्यासाठी तुमच्या खात्यात लॉग इन करा",
      mobileNumber: "मोबाईल नंबर",
      createAccount: "नवीन खाते तयार करा",
      farmDetails: "सुरुवात करण्यासाठी तुमचे तपशील प्रविष्ट करा",
      name: "नाव",
      acresOfLand: "जमिनीचे एकर",
      areaOfLand: "जमिनीचे क्षेत्र (उदा. गाव, शहर)",
      gallery: "गॅलरी",
      analyzing: "विश्लेषण करत आहे...",
      healthy: "निरोगी",
      treatment: "उपचार",
      error: "त्रुटी",
      success: "यशस्वी",
      fillAllFields: "कृपया सर्व फील्ड भरा.",
      invalidMobile: "कृपया वैध 10-अंकी मोबाइल नंबर प्रविष्ट करा.",
      accountCreated: "खाते यशस्वीरित्या तयार केले! आपण आता लॉग इन आहात.",
      accountCreateFailed: "खाते तयार करण्यात अयशस्वी. कृपया पुन्हा प्रयत्न करा.",
      noAccountFound: "या मोबाइल नंबरसह कोणतेही खाते आढळले नाही. कृपया नोंदणी करा.",
      alreadyHaveAccount: "आधीपासूनच खाते आहे?",
      dontHaveAccount: "खाते नाही?",
      goBack: "मागे जा",
      permissionDenied: "परवानगी नाकारली",
      locationPermission: "स्थानिक हवामान आणि सल्ला देण्यासाठी स्थान प्रवेश आवश्यक आहे.",
      micPermission: "व्हॉइस असिस्टंट वैशिष्ट्यासाठी मायक्रोफोन प्रवेश आवश्यक आहे.",
      cameraPermission: "रोग ओळख वैशिष्ट्यासाठी कॅमेरा प्रवेश आवश्यक आहे.",
      recommendationError: "शिफारसी मिळविण्यात अयशस्वी",
      captureError: "प्रतिमा कॅप्चर करण्यात अयशस्वी",
      selectImageError: "प्रतिमा निवडण्यात अयशस्वी",
      analyzeError: "प्रतिमा विश्लेषण करण्यात अयशस्वी",
      weatherError: "हवामान डेटा आणण्यात अयशस्वी",
      voiceDemoError: "या डेमोमध्ये व्हॉइस रेकग्निशन उपलब्ध नाही",
      initError: "अ‍ॅप सुरू करण्यात अयशस्वी. कृपया पुन्हा सुरू करा.",
      enterName: "तुमचे नाव प्रविष्ट करा",
      enterMobileNumber: "तुमचा मोबाइल नंबर प्रविष्ट करा",
      enterLandArea: "जमिनीचे क्षेत्र प्रविष्ट करा",
      farmSize: 'शेतीचा आकार (एकर)',
      location: 'ठिकाण',
      cameraPermissionError: 'कॅमेरा परवानगीची विनंती करण्यात अयशस्वी.',
      selectImageError: 'प्रतिमा निवडण्यात त्रुटी.',
      analyzing: 'प्रतिमेचे ವಿಶ्लेषण करा',
      analyzeError: 'प्रतिमा विश्लेषणात त्रुटी.',
      home: 'घर',
      crops: 'पिके',
      disease: 'रोग',
      weather: 'हवामान',
      gallery: 'गॅलरीतून निवडा',
      ok: 'ठीक आहे',
      cancel: 'रद्द करा',
      submit: 'प्रस्तुत करा',
      back: 'मागे',
      next: 'पुढे',
      done: 'पूर्ण झाले',
      save: 'जतन करा',
      edit: 'संपादित करा',
      delete: 'हटवा',
      add: 'जोडा',
      view: 'पहा',
      search: 'शोधा',
      clear: 'साफ करा',
      close: 'बंद करा',
      loading: 'लोड करत आहे',
      yes: 'होय',
      no: 'नाही',
      warning: 'चेतावणी',
      info: 'माहिती',
      status: 'स्थिती',
      retry: 'पुन्हा प्रयत्न करा',
      settings: 'सेटिंग्ज',
      profile: 'प्रोफाइल',
      logout: 'लॉग आउट',
      about: 'बद्दल',
      help: 'मदत',
      terms: 'अटी',
      privacy: 'गोपनीयता',
      notifications: 'सूचना',
      language: 'भाषा',
      theme: 'थीम',
      send: 'पाठवा',
      message: 'संदेश',
      call: 'कॉल करा',
      email: 'ईमेल',
      password: 'पासवर्ड',
      username: 'वापरकर्तानाव',
      name: 'नाव',
      mobile: 'मोबाइल',
      number: 'क्रमांक',
      code: 'कोड',
      verification: 'पडताळणी',
      resend: 'पुन्हा पाठवा',
      update: 'अद्यतनित करा',
      skip: 'वगळा',
      photo: 'फोटो',
      camera: 'कॅमेरा',
      image: 'प्रतिमा',
      video: 'व्हिडिओ',
      audio: 'ऑडिओ',
      record: 'रेकॉर्ड करा',
      play: 'प्ले करा',
      pause: 'विराम द्या',
      stop: 'थांबवा',
      voice: 'आवाज',
      assistant: 'सहाय्यक',
      microphone: 'मायक्रोफोन',
      speaker: 'स्पीकर',
      volume: 'आवाज',
      mute: 'निःशब्द करा',
      unmute: 'सशब्द करा',
      chat: 'चॅट',
      users: 'वापरकर्ते',
      groups: 'गट',
      contacts: 'संपर्क',
      friends: 'मित्र',
      requests: 'विनंत्या',
      pending: 'प्रलंबित',
      accepted: 'स्वीकारले',
      rejected: 'नाकारले',
      blocked: 'अवरोधित केले',
      unblock: 'अवरोधित करा',
      report: ' तक्रार करा',
      spam: 'स्पॅम',
      scam: 'घोटाळा',
      phishing: 'फिशिंग',
      hacking: 'हॅकिंग',
      abuse: 'गैरवापर',
      harassment: 'छळ',
      violence: 'हिंसा',
      hate: 'द्वेष',
      speech: 'भाषण',
      nudity: 'नग्नता',
      pornography: ' pornografi',
      self: 'स्वतः',
      harm: 'हानी',
      suicide: 'आत्महत्या',
      impersonation: ' प्रतिरूपण',
      copyright: 'कॉपीराइट',
      trademark: 'ट्रेडमार्क',
      infringement: 'उल्लंघन',
      other: 'इतर',
    }
  },
  bn: {
    translation: {
      welcome: "এগ্রোএআই-তে স্বাগতম",
      cropRecommendation: "ফসল সুপারিশ",
      diseaseDetection: "রোগ সনাক্তকরণ",
      weather: "আবহাওয়া",
      selectLanguage: "আপনার ভাষা নির্বাচন করুন",
      continue: "চালিয়ে যান",
      login: "লগইন করুন",
      register: "নিবন্ধন করুন",
      welcomeBack: "আবারও স্বাগতম!",
      loginToContinue: "চালিয়ে যেতে আপনার অ্যাকাউন্টে লগইন করুন",
      mobileNumber: "মোবাইল নম্বর",
      createAccount: "একটি নতুন অ্যাকাউন্ট তৈরি করুন",
      farmDetails: "শুরু করতে আপনার বিবরণ লিখুন",
      name: "নাম",
      acresOfLand: "জমির একর",
      areaOfLand: "জমির এলাকা (যেমন, গ্রাম, শহর)",
      gallery: "গ্যালারি",
      analyzing: "বিশ্লেষণ করা হচ্ছে...",
      healthy: "স্বাস্থ্যকর",
      treatment: "চিকিৎসা",
      error: "ত্রুটি",
      success: "সাফল্য",
      fillAllFields: "অনুগ্রহ করে সমস্ত ক্ষেত্র পূরণ করুন।",
      invalidMobile: "অনুগ্রহ করে একটি বৈধ 10-সংখ্যার মোবাইল নম্বর লিখুন।",
      accountCreated: "অ্যাকাউন্ট সফলভাবে তৈরি করা হয়েছে! আপনি এখন লগ ইন করেছেন।",
      accountCreateFailed: "অ্যাকাউন্ট তৈরি করতে ব্যর্থ। অনুগ্রহ করে আবার চেষ্টা করুন।",
      noAccountFound: "এই মোবাইল নম্বর দিয়ে কোনো অ্যাকাউন্ট পাওয়া যায়নি। অনুগ্রহ করে নিবন্ধন করুন।",
      alreadyHaveAccount: "ইতিমধ্যে একটি অ্যাকাউন্ট আছে?",
      dontHaveAccount: "অ্যাকাউন্ট নেই?",
      goBack: "ফিরে যান",
      permissionDenied: "অনুমতি প্রত্যাখ্যান করা হয়েছে",
      locationPermission: "স্থানীয় আবহাওয়া এবং পরামর্শ প্রদানের জন্য অবস্থান অ্যাক্সেস প্রয়োজন।",
      micPermission: "ভয়েস সহকারী বৈশিষ্ট্যের জন্য মাইক্রোফোন অ্যাক্সেস প্রয়োজন।",
      cameraPermission: "রোগ সনাক্তকরণ বৈশিষ্ট্যের জন্য ক্যামেরা অ্যাক্সেস প্রয়োজন।",
      recommendationError: "সুপারিশ পেতে ব্যর্থ",
      captureError: "ছবি ক্যাপচার করতে ব্যর্থ",
      selectImageError: "ছবি নির্বাচন করতে ব্যর্থ",
      analyzeError: "ছবি বিশ্লেষণ করতে ব্যর্থ",
      weatherError: "আবহাওয়ার ডেটা আনতে ব্যর্থ",
      voiceDemoError: "এই ডেমোতে ভয়েস রিকগনিশন উপলব্ধ নেই",
      initError: "অ্যাপটি শুরু করতে ব্যর্থ। অনুগ্রহ করে পুনরায় চালু করুন।",
      enterName: "আপনার নাম লিখুন",
      enterMobileNumber: "আপনার মোবাইল নম্বর লিখুন",
      enterLandArea: "জমির ক্ষেত্রফল লিখুন",
      farmSize: 'খামারের আকার (একর)',
      location: 'অবস্থান',
      cameraPermissionError: 'ক্যামেরার অনুমতি অনুরোধ করতে ব্যর্থ।',
      selectImageError: 'ছবি নির্বাচন করতে ত্রুটি।',
      analyzing: 'ছবি বিশ্লেষণ করুন',
      analyzeError: 'ছবি বিশ্লেষণে ত্রুটি।',
      home: 'বাড়ি',
      crops: 'ফসল',
      disease: 'রোগ',
      weather: 'আবহাওয়া',
      takePhoto: 'ছবি তোলা',
      gallery: 'গ্যালারি থেকে পছন্দ করুন',
      ok: 'ঠিক আছে',
      cancel: 'বাতিল করুন',
      submit: 'জমা দিন',
      back: 'পিছনে',
      next: 'পরবর্তী',
      done: 'সম্পন্ন',
      save: 'সংরক্ষণ করুন',
      edit: 'সম্পাদনা করুন',
      delete: 'মুছে ফেলুন',
      add: 'যোগ করুন',
      view: 'দেখুন',
      search: 'অনুসন্ধান করুন',
      clear: 'পরিষ্কার করুন',
      close: 'বন্ধ করুন',
      loading: 'লোড হচ্ছে',
      yes: 'হ্যাঁ',
      no: 'না',
      warning: 'সতর্কবার্তা',
      info: 'তথ্য',
      status: 'স্থিতি',
      retry: 'পুনরায় চেষ্টা করুন',
      settings: 'সেটিংস',
      profile: 'প্রোফাইল',
      logout: 'লগ আউট',
      about: 'সম্পর্কে',
      help: 'সাহায্য',
      terms: 'শর্তাবলী',
      privacy: 'গোপনীয়তা',
      notifications: 'বিজ্ঞপ্তি',
      language: 'ভাষা',
      theme: 'থিম',
      send: 'প্রেরণ করুন',
      message: 'বার্তা',
      call: 'কল করুন',
      email: 'ইমেল',
      password: 'পাসওয়ার্ড',
      username: 'ব্যবহারকারীর নাম',
      name: 'নাম',
      mobile: 'মোবাইল',
      number: 'নম্বর',
      code: 'কোড',
      verification: 'সত্যাপন',
      resend: 'পুনরায় পাঠান',
      update: 'হালনাগাদ করুন',
      skip: 'এড়িয়ে যান',
      photo: 'ছবি',
      camera: 'ক্যামেরা',
      image: 'চিত্র',
      video: 'ভিডিও',
      audio: 'অডিও',
      record: 'রেকর্ড করুন',
      play: 'চালান',
      pause: 'বিরতি',
      stop: 'থামান',
      voice: 'কণ্ঠস্বর',
      assistant: 'সহকারী',
      microphone: 'মাইক্রোফোন',
      speaker: 'স্পিকার',
      volume: 'ভলিউম',
      mute: 'নিঃশব্দ করুন',
      unmute: 'সশব্দ করুন',
      chat: 'চ্যাট',
      users: 'ব্যবহারকারী',
      groups: 'দল',
      contacts: 'পরিচিতি',
      friends: 'বন্ধু',
      requests: 'অনুরোধ',
      pending: 'বিচারাধীন',
      accepted: 'গৃহীত',
      rejected: 'প্রত্যাখ্যাত',
      blocked: 'অবরুদ্ধ',
      unblock: ' অবরোধ মুক্ত করুন',
      report: 'অভিযোগ করুন',
      spam: 'স্প্যাম',
      scam: 'কেলেঙ্কারি',
      phishing: 'ফিশিং',
      hacking: 'হ্যাকিং',
      abuse: 'অপব্যবহার',
      harassment: 'হয়রানি',
      violence: 'সহিংসতা',
      hate: 'ঘৃণা',
      speech: 'ንግግር',
      nudity: 'নগ্নতা',
      pornography: 'অশ্লীল রচনা',
      self: 'আত্ম',
      harm: 'ক্ষতি',
      suicide: 'আত্মহত্যা',
      impersonation: 'প্রতারণা',
      copyright: 'কপিরাইট',
      trademark: 'ট্রেডমার্ক',
      infringement: 'লঙ্ঘন',
      other: 'অন্যান্য',
    }
  }
};


class LanguageService {
  constructor() {
    this.currentLanguage = 'en';
    this.initialized = false;
  }

  async initialize() {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      const lng = stored || this.currentLanguage;

      await i18next
        .use(initReactI18next)
        .init({
          resources,
          lng,
          fallbackLng: 'en',
          interpolation: {
            escapeValue: false,
          },
        });

      this.currentLanguage = i18next.language || lng;
      this.initialized = true;

      // keep internal state in sync
      i18next.on('languageChanged', (lng) => {
        this.currentLanguage = lng;
      });
    } catch (error) {
      console.error('Language service initialization error:', error);
      throw error;
    }
  }

  t = (key, options) => {
    if (!this.initialized) return key;
    return i18next.t(key, options);
  };

  async changeLanguage(languageCode) {
    try {
      await i18next.changeLanguage(languageCode);
      this.currentLanguage = languageCode;
      await AsyncStorage.setItem(STORAGE_KEY, languageCode);
    } catch (error) {
      console.error('Language change error:', error);
      throw error;
    }
  }

  getCurrentLanguage() {
    return this.currentLanguage;
  }

  // <-- Added: subscription helper so callers can listen to language changes
  onLanguageChanged(cb) {
    if (typeof cb !== 'function') return () => {};
    i18next.on('languageChanged', cb);
    // return unsubscribe function
    return () => i18next.off('languageChanged', cb);
  }
  // <-- end added

  getAvailableLanguages() {
    return [
      { code: 'en', name: 'English' },
      { code: 'hi', name: 'हिन्दी' },
      { code: 'te', name: 'తెలుగు' },
      { code: 'ta', name: 'தமிழ்' },
      { code: 'kn', name: 'ಕನ್ನಡ' },
      { code: 'mr', name: 'मराठी' },
      { code: 'bn', name: 'বাংলা' },
      // Add more languages here
    ];
  }
}

export default new LanguageService();