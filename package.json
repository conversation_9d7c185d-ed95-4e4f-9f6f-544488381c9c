{"name": "agroai-expo-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest"}, "dependencies": {"@expo/vector-icons": "^15.0.2", "@react-native-async-storage/async-storage": "2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^6.5.7", "@react-navigation/native": "6.1.14", "@react-navigation/stack": "6.3.22", "axios": "^1.5.0", "expo": "^54.0.0", "expo-av": "~16.0.7", "expo-camera": "~17.0.8", "expo-constants": "~18.0.9", "expo-font": "~14.0.8", "expo-image-picker": "~17.0.8", "expo-location": "~19.0.7", "expo-permissions": "~14.2.1", "expo-speech": "~14.0.7", "expo-sqlite": "~16.0.8", "expo-status-bar": "~3.0.8", "i18next": "^23.5.1", "intl-pluralrules": "^2.0.1", "react": "19.1.0", "react-dom": "19.1.0", "react-i18next": "^13.2.2", "react-native": "0.81.4", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-web": "~0.21.0"}, "devDependencies": {"@babel/core": "^7.26.0", "jest": "~29.7.0"}, "private": true}